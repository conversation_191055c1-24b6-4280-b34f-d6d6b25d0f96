import { world, system, GameMode } from "@minecraft/server";
import { initEntityListeners } from "./entities/index";
import { handleAllItems, registerSandwichComponent } from "./items/index";
import { registerStairsComponent } from "./blocks/stairs";
const WEREWOLF_FOG_ID = "ditsh:werewolf_fog";
const DETECTION_RADIUS = 128;
system.runInterval(() => {
    try {
        handleAllItems();
        const allPlayers = world
            .getAllPlayers()
            .filter((player) => player.getGameMode() !== GameMode.Creative && player.getGameMode() !== GameMode.Spectator);
        for (const player of allPlayers) {
            try {
                const nearbyWerewolves = player.dimension.getEntities({
                    type: "ditsh:werewolf",
                    location: player.location,
                    maxDistance: DETECTION_RADIUS
                });
                const hasNearbyWerewolf = nearbyWerewolves.length > 0;
                const currentlyHasFog = player.getDynamicProperty("ditsh:werewolf_fog") === true;
                if (hasNearbyWerewolf && !currentlyHasFog) {
                    player.runCommand(`fog @s push ${WEREWOLF_FOG_ID} werewolf`);
                    player.setDynamicProperty("ditsh:werewolf_fog", true);
                }
                else if (!hasNearbyWerewolf && currentlyHasFog) {
                    player.runCommand(`fog @s remove werewolf`);
                    player.setDynamicProperty("ditsh:werewolf_fog", false);
                }
            }
            catch (error) {
                console.warn(`Failed to process werewolf fog for player: ${error}`);
            }
        }
    }
    catch (error) {
        console.warn(`Failed to run werewolf fog system: ${error}`);
    }
}, 20);
world.afterEvents.playerSpawn.subscribe((event) => {
    try {
        const player = event.player;
        player.runCommand(`fog @s remove werewolf`);
        player.setDynamicProperty("ditsh:werewolf_fog", false);
    }
    catch (error) {
        console.warn(`Failed to clear werewolf fog on player spawn: ${error}`);
    }
});
system.beforeEvents.startup.subscribe((event) => {
    try {
        registerSandwichComponent(event.itemComponentRegistry);
        registerStairsComponent(event.blockComponentRegistry);
    }
    catch (error) {
        console.warn(`Failed to register custom components: ${error}`);
    }
});
initEntityListeners();
