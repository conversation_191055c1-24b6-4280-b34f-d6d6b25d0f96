import { EntityComponentTypes, ItemLockMode, EquipmentSlot, ItemStack, system } from "@minecraft/server";
export class SandwichComponent {
    onConsume = (event) => {
        try {
            if (!event.source || event.source.typeId !== "minecraft:player") {
                return;
            }
            const player = event.source;
            player.addEffect("regeneration", 600, {
                amplifier: 0,
                showParticles: false
            });
            player.addEffect("speed", 600, {
                amplifier: 0,
                showParticles: false
            });
            system.runTimeout(() => {
                this.addAndLockSandwichInMainHand(player);
            }, 1);
        }
        catch (error) {
            console.warn(`Failed to handle sandwich consumption: ${error}`);
        }
    };
    addAndLockSandwichInMainHand = (player) => {
        try {
            const equippableComponent = player.getComponent(EntityComponentTypes.Equippable);
            if (!equippableComponent) {
                console.warn(`Failed to get equippable component for player ${player.name}`);
                return;
            }
            const sandwichItem = new ItemStack("ditsh:sandwich", 1);
            equippableComponent.setEquipment(EquipmentSlot.Mainhand, sandwichItem);
            const mainHandSlot = equippableComponent.getEquipmentSlot(EquipmentSlot.Mainhand);
            mainHandSlot.lockMode = ItemLockMode.slot;
            mainHandSlot.keepOnDeath = true;
        }
        catch (error) {
            console.warn(`Failed to add and lock sandwich in main hand for player ${player.name}: ${error}`);
        }
    };
}
