import { system, GameMode } from "@minecraft/server";
import { getDistance, getDirection } from "../utilities/vector3";
export function mxCheckForWallHit(mx) {
    try {
        const isDashing = mx.getProperty("ditsh:dashing");
        if (!isDashing)
            return;
        const location = { x: mx.location.x, y: mx.location.y + 2, z: mx.location.z };
        const direction = mx.getViewDirection();
        const offset = { x: location.x + direction.x * 3, y: location.y, z: location.z + direction.z * 3 };
        const block = mx.dimension.getBlock(offset);
        if (block && !block.isAir) {
            mx.triggerEvent("ditsh:on_dash_attack");
            mx.dimension.playSound("mob.ditsh.mx.hit_wall", location);
        }
    }
    catch (e) { }
    return;
}
export async function mxJump(mx) {
    try {
        mx.setProperty("ditsh:jumping", true);
        const nearbyPlayers = mx.dimension.getPlayers({
            type: "minecraft:player",
            location: mx.location,
            maxDistance: 64,
            excludeGameModes: [GameMode.Creative, GameMode.Spectator]
        });
        if (nearbyPlayers.length === 0) {
            mx.setProperty("ditsh:jumping", false);
            return;
        }
        let nearestPlayer = nearbyPlayers[0];
        let shortestDistance = getDistance(mx.location, nearestPlayer.location);
        for (const player of nearbyPlayers) {
            const distance = getDistance(mx.location, player.location);
            if (distance < shortestDistance) {
                shortestDistance = distance;
                nearestPlayer = player;
            }
        }
        for (const player of nearbyPlayers) {
            player.onScreenDisplay.setActionBar("§c§lMX is about to JUMP!§r");
        }
        await system.waitTicks(20);
        const minPower = 0.5;
        const maxPower = 8.0;
        const maxDistance = 64;
        const normalizedDistance = Math.min(shortestDistance, maxDistance) / maxDistance;
        const impulsePower = minPower + normalizedDistance * (maxPower - minPower);
        const direction = getDirection(mx.location, nearestPlayer.location);
        const impulseVector = {
            x: direction.x * impulsePower,
            y: Math.max(0.8, impulsePower * 0.6),
            z: direction.z * impulsePower
        };
        for (const player of nearbyPlayers) {
            try {
                player.addEffect("slowness", 200, {
                    amplifier: 4,
                    showParticles: false
                });
            }
            catch (e) {
            }
        }
        mx.applyImpulse(impulseVector);
        mx.dimension.playSound("mob.ditsh.mx.jump", mx.location);
        await system.waitTicks(10);
        mx.setProperty("ditsh:ground_check", true);
    }
    catch (e) {
        try {
            mx.setProperty("ditsh:jumping", false);
            mx.setProperty("ditsh:ground_check", false);
        }
        catch (resetError) { }
    }
    return;
}
