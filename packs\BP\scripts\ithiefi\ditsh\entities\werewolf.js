import { GameMode, EntityDamageCause } from "@minecraft/server";
import { getDistance } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";
const WEREWOLF_DAMAGE = 20;
const DETECTION_RADIUS = 128;
const RAYCAST_RADIUS = 5;
const RAYCAST_STEP = 2;
function isPlayerLookingAtWerewolf(player, werewolfLocation) {
    try {
        const playerLocation = {
            x: player.location.x,
            y: player.location.y + 1.6,
            z: player.location.z
        };
        const viewDirection = player.getViewDirection();
        const maxDistance = getDistance(playerLocation, werewolfLocation);
        const raycastStep = RAYCAST_STEP;
        const detectionRadius = RAYCAST_RADIUS;
        const raycastPoints = fixedLenRaycast(playerLocation, viewDirection, maxDistance, raycastStep);
        for (const rayPoint of raycastPoints) {
            const distanceToWerewolf = getDistance(rayPoint, werewolfLocation);
            if (distanceToWerewolf <= detectionRadius) {
                if (rayPoint.y >= werewolfLocation.y && rayPoint.y <= werewolfLocation.y + 3.0) {
                    return true;
                }
            }
        }
        return false;
    }
    catch (error) {
        return false;
    }
}
export function werewolfTeleportAndKill(werewolf) {
    try {
        const werewolfLocation = werewolf.location;
        const players = werewolf.dimension.getPlayers({
            location: werewolfLocation,
            maxDistance: DETECTION_RADIUS,
            excludeGameModes: [GameMode.Creative, GameMode.Spectator]
        });
        for (const player of players) {
            if (isPlayerLookingAtWerewolf(player, werewolfLocation)) {
                werewolf.teleport(player.location);
                player.applyDamage(WEREWOLF_DAMAGE, { cause: EntityDamageCause.entityAttack, damagingEntity: werewolf });
                werewolf.dimension.playSound("mob.ditsh.werewolf.kill", player.location);
                break;
            }
        }
    }
    catch (error) {
        console.warn(`Failed to handle werewolf teleport and kill: ${error}`);
    }
}
