import { GameMode } from "@minecraft/server";
import { getDistance } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";
const DARKNESS_DURATION = 100;
const RAYCAST_LENGTH = 128;
const RAYCAST_STEP = 2;
const RAYCAST_RADIUS = 5;
export function vitaMimicOnPlayerStartLooking(vitaMimic) {
    try {
        const vitaMimicLocation = vitaMimic.location;
        const players = vitaMimic.dimension.getPlayers({
            location: vitaMimicLocation,
            maxDistance: 128,
            excludeGameModes: [GameMode.Creative, GameMode.Spectator]
        });
        for (const player of players) {
            if (isPlayerLookingAtVitaMimic(player, vitaMimicLocation)) {
                player.addEffect("darkness", DARKNESS_DURATION, { amplifier: 0, showParticles: false });
            }
        }
    }
    catch (error) {
        console.warn(`Failed to apply Vita Mimic look effects: ${error}`);
    }
}
function isPlayerLookingAtVitaMimic(player, vitaMimicLocation) {
    try {
        const playerLocation = player.getHeadLocation();
        const viewDirection = player.getViewDirection();
        const maxDistance = RAYCAST_LENGTH;
        const raycastStep = RAYCAST_STEP;
        const detectionRadius = RAYCAST_RADIUS;
        const raycastPoints = fixedLenRaycast(playerLocation, viewDirection, maxDistance, raycastStep);
        for (const rayPoint of raycastPoints) {
            const distanceToVitaMimic = getDistance(rayPoint, vitaMimicLocation);
            if (distanceToVitaMimic <= detectionRadius) {
                if (rayPoint.y >= vitaMimicLocation.y && rayPoint.y <= vitaMimicLocation.y + 3.0) {
                    return true;
                }
            }
        }
        return false;
    }
    catch (error) {
        return false;
    }
}
