import { world, GameMode, EntityComponentTypes, EquipmentSlot } from "@minecraft/server";
import { SandwichComponent } from "./sandwich";
import { handleCoilForPlayer, cleanupCoilTrackers } from "./coils";
const ITEM_HANDLERS = new Map([
    ["ditsh:insane_decay_coil", (player) => handleCoilForPlayer(player, "ditsh:insane_decay_coil")],
    ["ditsh:speed_coil", (player) => handleCoilForPlayer(player, "ditsh:speed_coil")],
    ["ditsh:special_coil", (player) => handleCoilForPlayer(player, "ditsh:special_coil")]
]);
export function getPlayerMainHandItem(player) {
    try {
        const equippableComponent = player.getComponent(EntityComponentTypes.Equippable);
        if (!equippableComponent) {
            return undefined;
        }
        return equippableComponent.getEquipment(EquipmentSlot.Mainhand);
    }
    catch (error) {
        console.warn(`Failed to get main hand item for player ${player.name}: ${error}`);
        return undefined;
    }
}
export function registerSandwichComponent(itemComponentRegistry) {
    try {
        itemComponentRegistry.registerCustomComponent("ditsh:sandwich", new SandwichComponent());
    }
    catch (error) {
        console.warn(`Failed to register sandwich component: ${error}`);
    }
}
export function handleAllItems() {
    try {
        const allPlayers = world
            .getAllPlayers()
            .filter((player) => player.getGameMode() !== GameMode.Creative && player.getGameMode() !== GameMode.Spectator);
        const playersWithItems = new Set();
        for (const player of allPlayers) {
            try {
                const mainHandItem = getPlayerMainHandItem(player);
                if (mainHandItem) {
                    const handler = ITEM_HANDLERS.get(mainHandItem.typeId);
                    if (handler) {
                        handler(player);
                        playersWithItems.add(player.id);
                    }
                }
            }
            catch (playerError) {
                console.warn(`Failed to process items for player ${player.name}: ${playerError}`);
            }
        }
    }
    catch (error) {
        console.warn(`Failed to handle all items: ${error}`);
    }
}
export function cleanupAllItemTrackers() {
    try {
        cleanupCoilTrackers();
    }
    catch (error) {
        console.warn(`Failed to cleanup item trackers: ${error}`);
    }
}
